🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_07_10_28_56
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_07_10_35_22
使用模型: gpn_transformer
Actor参数数量: 4,383,364
Critic参数数量: 2,152,321

开始训练 Epoch 1/3
Batch 0: Reward: -71.9578, Loss: 5966.9595, Revenue: 0.4259, LoadBalance: 0.3437, Tasks: [S0:1004(54.1%), S1:218(11.7%), S2:634(34.2%)], ActorGrad: 28501.2832, CriticGrad: 4230.8115, Advantage: μ=-73.158, σ=25.193, range=[-140.66, -41.93]
Epoch 1, Batch 50/3125, loss: 4872.933↓, reward: -13.555↑, critic_reward: -3.744, revenue_rate: 0.3061, distance: 5.4478, memory: 0.0628, power: 0.2102, lr: 0.000400, took: 190.491s
Batch 50: Reward: 10.9004, Loss: 5682.8452, Revenue: 0.2726, LoadBalance: 0.7500, Tasks: [S0:442(36.3%), S1:372(30.6%), S2:402(33.1%)], ActorGrad: 7887.3413, CriticGrad: 244.1174, Advantage: μ=11.733, σ=60.089, range=[-32.37, 140.57]
Epoch 1, Batch 100/3125, loss: 4377.108↑, reward: -8.525↓, critic_reward: -4.190, revenue_rate: 0.2919, distance: 5.1504, memory: 0.0620, power: 0.2084, lr: 0.000400, took: 186.291s
Batch 100: Reward: 6.5767, Loss: 4885.3062, Revenue: 0.2864, LoadBalance: 0.7709, Tasks: [S0:451(32.8%), S1:442(32.1%), S2:483(35.1%)], ActorGrad: 7883.6450, CriticGrad: 198.6194, Advantage: μ=6.750, σ=56.852, range=[-40.51, 142.62]
Epoch 1, Batch 150/3125, loss: 5505.956↓, reward: 8.011↓, critic_reward: 2.436, revenue_rate: 0.2713, distance: 4.7440, memory: 0.0584, power: 0.2006, lr: 0.000400, took: 175.611s
Batch 150: Reward: 11.4516, Loss: 5311.7920, Revenue: 0.2498, LoadBalance: 0.7704, Tasks: [S0:429(35.3%), S1:364(29.9%), S2:423(34.8%)], ActorGrad: 5995.1260, CriticGrad: 174.9410, Advantage: μ=6.057, σ=59.055, range=[-51.26, 135.36]
Epoch 1, Batch 200/3125, loss: 4575.149↓, reward: -2.370↓, critic_reward: 2.600, revenue_rate: 0.2909, distance: 5.1654, memory: 0.0601, power: 0.2146, lr: 0.000400, took: 198.071s
Batch 200: Reward: -6.3236, Loss: 4347.6797, Revenue: 0.2571, LoadBalance: 0.6777, Tasks: [S0:433(35.6%), S1:380(31.2%), S2:403(33.1%)], ActorGrad: 7611.0078, CriticGrad: 135.3450, Advantage: μ=-5.086, σ=54.183, range=[-55.58, 138.66]
Epoch 1, Batch 250/3125, loss: 5088.474↓, reward: 6.414↓, critic_reward: 0.741, revenue_rate: 0.2705, distance: 4.6877, memory: 0.0521, power: 0.1991, lr: 0.000400, took: 178.121s
Batch 250: Reward: 28.7724, Loss: 6941.1748, Revenue: 0.2796, LoadBalance: 0.8051, Tasks: [S0:432(32.9%), S1:464(35.4%), S2:416(31.7%)], ActorGrad: 9851.1270, CriticGrad: 447.4475, Advantage: μ=20.181, σ=63.921, range=[-30.71, 132.11]
Epoch 1, Batch 300/3125, loss: 5247.430↑, reward: 7.474↓, critic_reward: 3.841, revenue_rate: 0.2738, distance: 4.8263, memory: 0.0587, power: 0.2053, lr: 0.000400, took: 184.727s
Batch 300: Reward: 18.2141, Loss: 6144.5830, Revenue: 0.2851, LoadBalance: 0.7798, Tasks: [S0:461(35.1%), S1:406(30.9%), S2:445(33.9%)], ActorGrad: 7454.7090, CriticGrad: 259.5578, Advantage: μ=11.100, σ=62.359, range=[-42.90, 133.18]
Epoch 1, Batch 350/3125, loss: 5379.086↑, reward: 9.528↑, critic_reward: 4.701, revenue_rate: 0.2780, distance: 4.9086, memory: 0.0707, power: 0.2082, lr: 0.000400, took: 185.656s
Batch 350: Reward: -3.9929, Loss: 3284.3125, Revenue: 0.2494, LoadBalance: 0.7574, Tasks: [S0:395(33.4%), S1:371(31.3%), S2:418(35.3%)], ActorGrad: 6350.6577, CriticGrad: 133.0598, Advantage: μ=-7.373, σ=47.766, range=[-38.32, 139.03]
Epoch 1, Batch 400/3125, loss: 5481.571↓, reward: 9.930↓, critic_reward: 5.691, revenue_rate: 0.2723, distance: 4.7807, memory: 0.0636, power: 0.2060, lr: 0.000400, took: 181.827s
Batch 400: Reward: -14.5592, Loss: 2444.7375, Revenue: 0.2498, LoadBalance: 0.7437, Tasks: [S0:401(34.8%), S1:414(35.9%), S2:337(29.3%)], ActorGrad: 5499.4502, CriticGrad: 315.4578, Advantage: μ=-20.324, σ=45.795, range=[-61.37, 151.38]
Epoch 1, Batch 450/3125, loss: 5619.311↑, reward: 11.644↑, critic_reward: 6.009, revenue_rate: 0.2747, distance: 4.7897, memory: 0.0544, power: 0.2065, lr: 0.000400, took: 186.301s
Batch 450: Reward: 7.9150, Loss: 5912.7139, Revenue: 0.2781, LoadBalance: 0.7259, Tasks: [S0:479(36.5%), S1:353(26.9%), S2:480(36.6%)], ActorGrad: 7342.6973, CriticGrad: 118.1900, Advantage: μ=1.367, σ=62.174, range=[-58.18, 133.83]
Epoch 1, Batch 500/3125, loss: 19267.620↑, reward: -68.945↓, critic_reward: 4.330, revenue_rate: 0.4147, distance: 8.0319, memory: 0.0730, power: 0.3325, lr: 0.000400, took: 305.700s
Batch 500: Reward: -105.5214, Loss: 11114.2012, Revenue: 0.5821, LoadBalance: 0.1414, Tasks: [S0:1175(37.1%), S1:104(3.3%), S2:1889(59.6%)], ActorGrad: 43793.8438, CriticGrad: 1469.3634, Advantage: μ=-100.958, σ=30.845, range=[-219.46, -70.63]
Epoch 1, Batch 550/3125, loss: 5219.581↓, reward: -29.069↑, critic_reward: -5.492, revenue_rate: 0.3561, distance: 6.8034, memory: 0.0771, power: 0.2813, lr: 0.000400, took: 254.406s
Batch 550: Reward: -9.1148, Loss: 3805.2754, Revenue: 0.2616, LoadBalance: 0.7065, Tasks: [S0:457(35.7%), S1:416(32.5%), S2:407(31.8%)], ActorGrad: 10149.7930, CriticGrad: 145.8465, Advantage: μ=-5.296, σ=51.131, range=[-62.53, 144.86]
Epoch 1, Batch 600/3125, loss: 5295.696↓, reward: 7.180↓, critic_reward: 1.902, revenue_rate: 0.2695, distance: 4.7255, memory: 0.0571, power: 0.2036, lr: 0.000400, took: 184.710s
Batch 600: Reward: 0.7062, Loss: 4367.0693, Revenue: 0.3251, LoadBalance: 0.7619, Tasks: [S0:465(30.3%), S1:529(34.4%), S2:542(35.3%)], ActorGrad: 5907.9365, CriticGrad: 133.1857, Advantage: μ=-3.530, σ=54.397, range=[-41.86, 130.26]
Epoch 1, Batch 650/3125, loss: 4757.648↓, reward: 0.795↓, critic_reward: 4.088, revenue_rate: 0.2809, distance: 4.9614, memory: 0.0552, power: 0.2116, lr: 0.000400, took: 189.976s
Batch 650: Reward: -11.0040, Loss: 2645.4033, Revenue: 0.2676, LoadBalance: 0.7754, Tasks: [S0:474(38.0%), S1:379(30.4%), S2:395(31.7%)], ActorGrad: 4487.5649, CriticGrad: 198.9311, Advantage: μ=-12.363, σ=42.585, range=[-45.54, 163.54]
Epoch 1, Batch 700/3125, loss: 4963.894↑, reward: 3.651↑, critic_reward: 1.456, revenue_rate: 0.2738, distance: 4.7976, memory: 0.0668, power: 0.1997, lr: 0.000400, took: 179.443s
Batch 700: Reward: 17.5255, Loss: 5716.7856, Revenue: 0.2871, LoadBalance: 0.7535, Tasks: [S0:496(36.9%), S1:443(33.0%), S2:405(30.1%)], ActorGrad: 6231.4565, CriticGrad: 260.5668, Advantage: μ=12.603, σ=60.093, range=[-38.49, 127.38]
Epoch 1, Batch 750/3125, loss: 5855.890↑, reward: 12.855↑, critic_reward: 3.423, revenue_rate: 0.2697, distance: 4.7057, memory: 0.0508, power: 0.2012, lr: 0.000400, took: 180.777s
Batch 750: Reward: -11.7010, Loss: 2758.5381, Revenue: 0.2715, LoadBalance: 0.7350, Tasks: [S0:452(34.5%), S1:463(35.3%), S2:397(30.3%)], ActorGrad: 3670.6230, CriticGrad: 219.0954, Advantage: μ=-14.141, σ=42.922, range=[-45.36, 123.20]
Epoch 1, Batch 800/3125, loss: 5233.323↑, reward: 8.533↑, critic_reward: 5.221, revenue_rate: 0.2751, distance: 4.8674, memory: 0.0581, power: 0.2087, lr: 0.000400, took: 186.300s
Batch 800: Reward: 10.6211, Loss: 5309.1602, Revenue: 0.2965, LoadBalance: 0.7483, Tasks: [S0:523(36.3%), S1:459(31.9%), S2:458(31.8%)], ActorGrad: 7704.3984, CriticGrad: 121.9803, Advantage: μ=4.136, σ=59.191, range=[-67.48, 126.85]
Epoch 1, Batch 850/3125, loss: 5851.377↓, reward: 13.165↓, critic_reward: 6.091, revenue_rate: 0.2735, distance: 4.7794, memory: 0.0658, power: 0.2069, lr: 0.000400, took: 188.719s
Batch 850: Reward: -0.5855, Loss: 3947.5444, Revenue: 0.2647, LoadBalance: 0.7809, Tasks: [S0:452(34.5%), S1:409(31.2%), S2:451(34.4%)], ActorGrad: 7163.7852, CriticGrad: 183.2601, Advantage: μ=-5.756, σ=51.903, range=[-42.52, 135.22]
Epoch 1, Batch 900/3125, loss: 5927.784↑, reward: 14.871↑, critic_reward: 6.481, revenue_rate: 0.2749, distance: 4.8355, memory: 0.0527, power: 0.2084, lr: 0.000400, took: 186.812s
Batch 900: Reward: 27.7259, Loss: 6906.4438, Revenue: 0.2621, LoadBalance: 0.7850, Tasks: [S0:460(35.9%), S1:412(32.2%), S2:408(31.9%)], ActorGrad: 7249.7100, CriticGrad: 322.5930, Advantage: μ=16.452, σ=64.727, range=[-40.70, 132.54]
Epoch 1, Batch 950/3125, loss: 5259.541↓, reward: 8.793↓, critic_reward: 6.962, revenue_rate: 0.2789, distance: 4.9410, memory: 0.0629, power: 0.2120, lr: 0.000400, took: 195.625s
Batch 950: Reward: 5.7885, Loss: 4661.5029, Revenue: 0.2836, LoadBalance: 0.7734, Tasks: [S0:441(31.3%), S1:453(32.2%), S2:514(36.5%)], ActorGrad: 7546.5767, CriticGrad: 135.9654, Advantage: μ=-0.814, σ=56.052, range=[-58.74, 136.03]
Epoch 1, Batch 1000/3125, loss: 5538.637↑, reward: 11.033↑, critic_reward: 6.846, revenue_rate: 0.2723, distance: 4.8322, memory: 0.0669, power: 0.2086, lr: 0.000400, took: 205.325s
Batch 1000: Reward: 13.5713, Loss: 5986.5132, Revenue: 0.3059, LoadBalance: 0.7731, Tasks: [S0:453(30.8%), S1:523(35.5%), S2:496(33.7%)], ActorGrad: 7765.1880, CriticGrad: 179.9991, Advantage: μ=5.141, σ=62.335, range=[-42.76, 133.73]
Epoch 1, Batch 1050/3125, loss: 5492.032↑, reward: 10.118↑, critic_reward: 7.169, revenue_rate: 0.2805, distance: 4.9873, memory: 0.0625, power: 0.2141, lr: 0.000400, took: 226.380s
Batch 1050: Reward: 43.6488, Loss: 8919.3291, Revenue: 0.2706, LoadBalance: 0.7934, Tasks: [S0:450(33.5%), S1:432(32.1%), S2:462(34.4%)], ActorGrad: 8915.0381, CriticGrad: 555.4062, Advantage: μ=28.288, σ=69.580, range=[-41.99, 121.88]
Epoch 1, Batch 1100/3125, loss: 5389.843↓, reward: 9.789↓, critic_reward: 7.425, revenue_rate: 0.2710, distance: 4.7512, memory: 0.0655, power: 0.2065, lr: 0.000400, took: 200.727s
Batch 1100: Reward: 25.8627, Loss: 7533.7285, Revenue: 0.3108, LoadBalance: 0.7631, Tasks: [S0:500(33.2%), S1:444(29.5%), S2:560(37.2%)], ActorGrad: 8582.5303, CriticGrad: 328.7143, Advantage: μ=14.660, σ=67.792, range=[-52.55, 132.22]
Epoch 1, Batch 1150/3125, loss: 5283.648↓, reward: 8.961↓, critic_reward: 7.169, revenue_rate: 0.2687, distance: 4.6839, memory: 0.0627, power: 0.2041, lr: 0.000400, took: 186.821s
Batch 1150: Reward: 1.2684, Loss: 3992.6450, Revenue: 0.2623, LoadBalance: 0.7653, Tasks: [S0:461(36.0%), S1:411(32.1%), S2:408(31.9%)], ActorGrad: 6152.8896, CriticGrad: 144.1740, Advantage: μ=-4.774, σ=52.247, range=[-52.57, 135.04]
