🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_06_10_30_25
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_06_10_30_29
使用模型: gpn_transformer
Actor参数数量: 4,383,364
Critic参数数量: 2,152,321

开始训练 Epoch 1/3
Batch 0: Reward: -115.7532, Loss: 15383.2354, Revenue: 0.4165, LoadBalance: 0.3545, Tasks: [S0:965(52.9%), S1:213(11.7%), S2:646(35.4%)], ActorGrad: 43828.3477, CriticGrad: 7158.8804, Advantage: μ=-116.867, σ=42.202, range=[-237.54, -67.74]
Batch 5: Reward: -67.9467, Loss: 8557.4062, Revenue: 0.2924, LoadBalance: 0.5366, Tasks: [S0:246(19.2%), S1:484(37.8%), S2:550(43.0%)], ActorGrad: 14539.3135, CriticGrad: 1007.1462, Advantage: μ=-52.209, σ=54.752, range=[-133.24, 215.27]
Epoch 1, Batch 10/32, loss: 14862.115↓, reward: -91.293↑, critic_reward: -2.798, revenue_rate: 0.3592, distance: 6.7089, memory: 0.0433, power: 0.2470, lr: 0.000400, took: 50.409s
Batch 10: Reward: -119.1300, Loss: 19124.0332, Revenue: 0.3288, LoadBalance: 0.4201, Tasks: [S0:299(21.2%), S1:339(24.1%), S2:770(54.7%)], ActorGrad: 24043.8086, CriticGrad: 1671.6206, Advantage: μ=-91.690, σ=61.980, range=[-229.34, -24.62]
Batch 15: Reward: 10.4532, Loss: 18354.5938, Revenue: 0.2817, LoadBalance: 0.7036, Tasks: [S0:389(32.0%), S1:499(41.0%), S2:328(27.0%)], ActorGrad: 14909.4717, CriticGrad: 368.3897, Advantage: μ=11.282, σ=103.265, range=[-133.34, 232.32]
Epoch 1, Batch 20/32, loss: 12656.863↓, reward: -63.039↑, critic_reward: -4.413, revenue_rate: 0.3322, distance: 6.1691, memory: 0.0847, power: 0.2279, lr: 0.000400, took: 43.503s
Batch 20: Reward: -34.7318, Loss: 8250.8184, Revenue: 0.3465, LoadBalance: 0.6718, Tasks: [S0:444(28.9%), S1:420(27.3%), S2:672(43.8%)], ActorGrad: 9427.4072, CriticGrad: 449.7607, Advantage: μ=-23.601, σ=68.470, range=[-73.72, 232.08]
Batch 25: Reward: -33.7482, Loss: 8216.6846, Revenue: 0.3033, LoadBalance: 0.6711, Tasks: [S0:531(39.5%), S1:492(36.6%), S2:321(23.9%)], ActorGrad: 11128.4736, CriticGrad: 483.9869, Advantage: μ=-22.783, σ=68.582, range=[-84.08, 273.12]
Epoch 1, Batch 30/32, loss: 14919.304↑, reward: 3.490↑, critic_reward: -4.795, revenue_rate: 0.2967, distance: 5.1915, memory: 0.0524, power: 0.2053, lr: 0.000400, took: 39.605s
Batch 30: Reward: 40.9201, Loss: 23410.0586, Revenue: 0.2649, LoadBalance: 0.7344, Tasks: [S0:356(29.3%), S1:404(33.2%), S2:456(37.5%)], ActorGrad: 20993.2109, CriticGrad: 799.2822, Advantage: μ=34.154, σ=111.392, range=[-127.33, 230.24]

📊 Epoch 1 训练统计:
  平均奖励: -44.9361
  平均损失: 14659.9114
  平均收益率: 0.3241
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -42.093, revenue_rate: 0.2990, efficiency: 0.0980, distance: 4.9911, memory: -0.0049, power: 0.1580
Test Batch 1/4, reward: -28.491, revenue_rate: 0.2867, efficiency: 0.0916, distance: 5.0033, memory: 0.0831, power: 0.1553
Test Batch 2/4, reward: 7.594, revenue_rate: 0.3333, efficiency: 0.1264, distance: 5.7734, memory: 0.0468, power: 0.1856
Test Batch 3/4, reward: -46.503, revenue_rate: 0.2191, efficiency: 0.0504, distance: 2.8245, memory: -0.0440, power: 0.1162
Test Summary - Avg reward: -22.017±97.070, revenue_rate: 0.3028±0.0449, efficiency: 0.1031, completion_rate: 0.3376, distance: 5.1587, memory: 0.0382, power: 0.1643
Load Balance - Avg balance score: 0.6973±0.1467
Task Distribution by Satellite:
  Satellite 1: 1100 tasks (33.45%)
  Satellite 2: 1252 tasks (38.08%)
  Satellite 3: 936 tasks (28.47%)
✅ 验证完成 - Epoch 1, reward: -22.017, revenue_rate: 0.3028, distance: 5.1587, memory: 0.0382, power: 0.1643
  ⚠️ 欠拟合: 训练验证差距 = -22.9192
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_06_10_30_29 (验证集奖励: -22.0169)

开始训练 Epoch 2/3
Batch 0: Reward: 13.3972, Loss: 18181.0137, Revenue: 0.2519, LoadBalance: 0.7323, Tasks: [S0:403(35.0%), S1:425(36.9%), S2:324(28.1%)], ActorGrad: 15491.7783, CriticGrad: 423.5239, Advantage: μ=13.535, σ=102.545, range=[-97.36, 266.19]
Batch 5: Reward: 27.0781, Loss: 19649.4062, Revenue: 0.3006, LoadBalance: 0.7694, Tasks: [S0:404(29.4%), S1:458(33.3%), S2:514(37.4%)], ActorGrad: 14858.6699, CriticGrad: 719.7947, Advantage: μ=22.377, σ=104.933, range=[-50.38, 234.24]
Epoch 2, Batch 10/32, loss: 17316.376↓, reward: 17.471↓, critic_reward: -2.474, revenue_rate: 0.2697, distance: 4.6920, memory: 0.0435, power: 0.1919, lr: 0.000400, took: 39.985s
Batch 10: Reward: 16.7735, Loss: 18322.7617, Revenue: 0.2707, LoadBalance: 0.7557, Tasks: [S0:453(36.3%), S1:415(33.3%), S2:380(30.4%)], ActorGrad: 16526.1777, CriticGrad: 459.1081, Advantage: μ=12.276, σ=103.072, range=[-119.88, 261.00]
Batch 15: Reward: -48.5227, Loss: 5864.4727, Revenue: 0.2721, LoadBalance: 0.6408, Tasks: [S0:307(23.4%), S1:564(43.0%), S2:441(33.6%)], ActorGrad: 10050.8242, CriticGrad: 1229.4635, Advantage: μ=-42.094, σ=48.262, range=[-94.96, 201.47]
Epoch 2, Batch 20/32, loss: 15642.154↓, reward: 6.938↓, critic_reward: 1.709, revenue_rate: 0.2922, distance: 5.1780, memory: 0.0555, power: 0.2088, lr: 0.000400, took: 38.985s
Batch 20: Reward: -26.5121, Loss: 8952.8398, Revenue: 0.2626, LoadBalance: 0.7089, Tasks: [S0:453(36.3%), S1:370(29.6%), S2:425(34.1%)], ActorGrad: 12811.3799, CriticGrad: 587.1507, Advantage: μ=-21.934, σ=71.766, range=[-82.23, 268.11]
Batch 25: Reward: -5.3004, Loss: 13470.3008, Revenue: 0.2814, LoadBalance: 0.6920, Tasks: [S0:347(26.4%), S1:428(32.6%), S2:537(40.9%)], ActorGrad: 12471.6182, CriticGrad: 345.9560, Advantage: μ=-4.048, σ=89.957, range=[-66.64, 232.02]
Epoch 2, Batch 30/32, loss: 14461.263↑, reward: 1.054↑, critic_reward: 0.210, revenue_rate: 0.2942, distance: 5.1911, memory: 0.0473, power: 0.2107, lr: 0.000400, took: 38.954s
Batch 30: Reward: 10.5819, Loss: 14625.2725, Revenue: 0.2993, LoadBalance: 0.7803, Tasks: [S0:463(33.6%), S1:497(36.1%), S2:416(30.2%)], ActorGrad: 13779.3320, CriticGrad: 349.2679, Advantage: μ=8.763, σ=93.119, range=[-63.10, 234.48]

📊 Epoch 2 训练统计:
  平均奖励: 7.8423
  平均损失: 15701.4993
  平均收益率: 0.2827
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -43.727, revenue_rate: 0.3524, efficiency: 0.1441, distance: 6.2116, memory: 0.0363, power: 0.2056
Test Batch 1/4, reward: -47.102, revenue_rate: 0.3197, efficiency: 0.1177, distance: 5.4979, memory: 0.0307, power: 0.1721
Test Batch 2/4, reward: -43.569, revenue_rate: 0.2816, efficiency: 0.0900, distance: 4.7693, memory: 0.0117, power: 0.1600
Test Batch 3/4, reward: -57.723, revenue_rate: 0.2783, efficiency: 0.0863, distance: 4.4570, memory: 0.1562, power: 0.1410
Test Summary - Avg reward: -45.316±83.819, revenue_rate: 0.3163±0.0481, efficiency: 0.1160, completion_rate: 0.3633, distance: 5.4515, memory: 0.0314, power: 0.1777
Load Balance - Avg balance score: 0.6261±0.1591
Task Distribution by Satellite:
  Satellite 1: 1067 tasks (30.11%)
  Satellite 2: 1581 tasks (44.61%)
  Satellite 3: 896 tasks (25.28%)
✅ 验证完成 - Epoch 2, reward: -45.316, revenue_rate: 0.3163, distance: 5.4515, memory: 0.0314, power: 0.1777
  ⚠️ 过拟合: 训练验证差距 = 53.1586

开始训练 Epoch 3/3
Batch 0: Reward: -47.8048, Loss: 9104.1836, Revenue: 0.3157, LoadBalance: 0.5992, Tasks: [S0:465(32.3%), S1:647(44.9%), S2:328(22.8%)], ActorGrad: 11017.4092, CriticGrad: 990.3595, Advantage: μ=-37.193, σ=66.499, range=[-93.66, 228.94]
Batch 5: Reward: 6.9675, Loss: 13804.7910, Revenue: 0.2577, LoadBalance: 0.7253, Tasks: [S0:416(35.1%), S1:337(28.5%), S2:431(36.4%)], ActorGrad: 11938.0840, CriticGrad: 322.4867, Advantage: μ=6.098, σ=90.868, range=[-58.72, 231.26]
Epoch 3, Batch 10/32, loss: 13155.416↓, reward: -8.808↑, critic_reward: -0.984, revenue_rate: 0.2686, distance: 4.6406, memory: 0.0527, power: 0.1921, lr: 0.000400, took: 40.039s
Batch 10: Reward: -20.1748, Loss: 8920.4961, Revenue: 0.2737, LoadBalance: 0.7116, Tasks: [S0:407(31.8%), S1:489(38.2%), S2:384(30.0%)], ActorGrad: 8348.6904, CriticGrad: 406.4860, Advantage: μ=-14.917, σ=73.280, range=[-77.45, 237.80]
Batch 15: Reward: -10.4742, Loss: 13701.2891, Revenue: 0.3134, LoadBalance: 0.6990, Tasks: [S0:471(32.0%), S1:404(27.4%), S2:597(40.6%)], ActorGrad: 13848.5830, CriticGrad: 270.1352, Advantage: μ=-7.013, σ=90.491, range=[-68.28, 237.05]
Epoch 3, Batch 20/32, loss: 13659.705↑, reward: 0.834↑, critic_reward: -1.435, revenue_rate: 0.2883, distance: 5.1273, memory: 0.0464, power: 0.2100, lr: 0.000400, took: 38.226s
Batch 20: Reward: 9.2019, Loss: 13103.0596, Revenue: 0.2991, LoadBalance: 0.7854, Tasks: [S0:496(35.2%), S1:482(34.2%), S2:430(30.5%)], ActorGrad: 11483.2441, CriticGrad: 285.2548, Advantage: μ=8.105, σ=88.560, range=[-47.22, 235.74]
Batch 25: Reward: 21.3906, Loss: 17736.3633, Revenue: 0.2490, LoadBalance: 0.7474, Tasks: [S0:352(29.7%), S1:385(32.5%), S2:447(37.8%)], ActorGrad: 14192.0420, CriticGrad: 475.8202, Advantage: μ=16.590, σ=100.922, range=[-79.84, 229.09]
Epoch 3, Batch 30/32, loss: 17562.067↑, reward: 25.479↓, critic_reward: -0.605, revenue_rate: 0.2856, distance: 5.0104, memory: 0.0427, power: 0.2079, lr: 0.000400, took: 38.133s
Batch 30: Reward: 35.3507, Loss: 18953.0332, Revenue: 0.2272, LoadBalance: 0.7165, Tasks: [S0:356(32.7%), S1:332(30.5%), S2:400(36.8%)], ActorGrad: 14423.1201, CriticGrad: 695.2004, Advantage: μ=26.445, σ=102.193, range=[-66.25, 188.60]

📊 Epoch 3 训练统计:
  平均奖励: 7.8728
  平均损失: 15058.4570
  平均收益率: 0.2761
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -11.522, revenue_rate: 0.3120, efficiency: 0.1089, distance: 5.4136, memory: 0.0408, power: 0.1711
Test Batch 1/4, reward: -19.992, revenue_rate: 0.2942, efficiency: 0.0999, distance: 5.2198, memory: 0.0521, power: 0.1665
Test Batch 2/4, reward: 25.246, revenue_rate: 0.3703, efficiency: 0.1585, distance: 6.5557, memory: -0.0266, power: 0.2189
Test Batch 3/4, reward: -48.695, revenue_rate: 0.2408, efficiency: 0.0765, distance: 4.0851, memory: 0.1541, power: 0.1464
Test Summary - Avg reward: -3.954±111.321, revenue_rate: 0.3221±0.0555, efficiency: 0.1206, completion_rate: 0.3700, distance: 5.6639, memory: 0.0274, power: 0.1839
Load Balance - Avg balance score: 0.7170±0.1273
Task Distribution by Satellite:
  Satellite 1: 1270 tasks (35.16%)
  Satellite 2: 1245 tasks (34.47%)
  Satellite 3: 1097 tasks (30.37%)
✅ 验证完成 - Epoch 3, reward: -3.954, revenue_rate: 0.3221, distance: 5.6639, memory: 0.0274, power: 0.1839
  ⚠️ 过拟合: 训练验证差距 = 11.8267
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_06_10_30_29 (验证集奖励: -3.9539)
训练完成

开始测试模型...
Test Batch 0/4, reward: 31.125, revenue_rate: 0.3134, efficiency: 0.1065, distance: 5.0975, memory: 0.0161, power: 0.1717
Test Batch 1/4, reward: 22.736, revenue_rate: 0.2916, efficiency: 0.0985, distance: 5.0423, memory: 0.0538, power: 0.1630
Test Batch 2/4, reward: -1.079, revenue_rate: 0.2867, efficiency: 0.0944, distance: 4.8774, memory: 0.0574, power: 0.1550
Test Batch 3/4, reward: 20.660, revenue_rate: 0.3090, efficiency: 0.1197, distance: 6.2503, memory: 0.1027, power: 0.1896
Test Summary - Avg reward: 17.717±139.734, revenue_rate: 0.2977±0.0403, efficiency: 0.1006, completion_rate: 0.3377, distance: 5.0556, memory: 0.0448, power: 0.1643
Load Balance - Avg balance score: 0.7116±0.1615
Task Distribution by Satellite:
  Satellite 1: 1119 tasks (34.03%)
  Satellite 2: 1130 tasks (34.37%)
  Satellite 3: 1039 tasks (31.60%)
测试完成 - 平均奖励: 17.717, 平均星座收益率: 0.2977
✅ 模式 COOPERATIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_06_10_30_29
   平均奖励: 17.7168
   收益率: 0.2977

🚀 [2/3] 开始训练模式: COMPETITIVE

============================================================
开始训练星座模式: COMPETITIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: competitive
verbose: True
2025_08_06_10_38_39
使用模型: gpn_transformer
Actor参数数量: 4,383,364
Critic参数数量: 2,152,321

开始训练 Epoch 1/3
Batch 0: Reward: -75.3415, Loss: 7872.8867, Revenue: 0.3706, LoadBalance: 0.4082, Tasks: [S0:385(26.2%), S1:290(19.7%), S2:797(54.1%)], ActorGrad: 23068.4062, CriticGrad: 6736.0322, Advantage: μ=-75.768, σ=46.913, range=[-274.43, -26.07]
Batch 5: Reward: -94.4418, Loss: 10388.1650, Revenue: 0.4036, LoadBalance: 0.3101, Tasks: [S0:461(26.7%), S1:254(14.7%), S2:1013(58.6%)], ActorGrad: 10444.5010, CriticGrad: 1460.9375, Advantage: μ=-90.239, σ=48.141, range=[-239.82, -33.30]
Epoch 1, Batch 10/32, loss: 63183.714↓, reward: -137.742↑, critic_reward: -3.483, revenue_rate: 0.4079, distance: 7.7655, memory: 0.0488, power: 0.2660, lr: 0.000400, took: 55.131s
Batch 10: Reward: -6.1101, Loss: 4249.3594, Revenue: 0.2527, LoadBalance: 0.6963, Tasks: [S0:316(29.0%), S1:341(31.3%), S2:431(39.6%)], ActorGrad: 3704.6079, CriticGrad: 143.6240, Advantage: μ=-1.368, σ=53.846, range=[-40.21, 144.73]
Batch 15: Reward: -23.2924, Loss: 4410.3037, Revenue: 0.3076, LoadBalance: 0.6172, Tasks: [S0:296(23.1%), S1:436(34.1%), S2:548(42.8%)], ActorGrad: 5559.9263, CriticGrad: 370.2451, Advantage: μ=-15.128, σ=52.852, range=[-71.96, 151.89]
Epoch 1, Batch 20/32, loss: 4131.611↓, reward: -11.065↓, critic_reward: -4.661, revenue_rate: 0.2920, distance: 5.1229, memory: 0.0474, power: 0.1941, lr: 0.000400, took: 35.722s
Batch 20: Reward: 29.7209, Loss: 7946.3193, Revenue: 0.3107, LoadBalance: 0.7732, Tasks: [S0:415(30.9%), S1:465(34.6%), S2:464(34.5%)], ActorGrad: 6278.5317, CriticGrad: 546.9582, Advantage: μ=27.136, σ=65.999, range=[-27.06, 135.85]
Batch 25: Reward: -6.7435, Loss: 4406.8594, Revenue: 0.2692, LoadBalance: 0.7174, Tasks: [S0:460(36.9%), S1:390(31.2%), S2:398(31.9%)], ActorGrad: 5341.9438, CriticGrad: 252.5935, Advantage: μ=-1.825, σ=54.685, range=[-41.18, 139.63]
Epoch 1, Batch 30/32, loss: 5594.458↓, reward: 6.021↓, critic_reward: -4.318, revenue_rate: 0.2875, distance: 4.9191, memory: 0.0476, power: 0.1989, lr: 0.000400, took: 37.155s
Batch 30: Reward: 16.9762, Loss: 6410.7666, Revenue: 0.3011, LoadBalance: 0.7751, Tasks: [S0:418(31.9%), S1:442(33.7%), S2:452(34.5%)], ActorGrad: 6929.4790, CriticGrad: 513.0278, Advantage: μ=16.023, σ=62.611, range=[-40.08, 146.27]

📊 Epoch 1 训练统计:
  平均奖励: -44.1671
  平均损失: 23126.9962
  平均收益率: 0.3245
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -45.179, revenue_rate: 0.3066, efficiency: 0.1068, distance: 5.1924, memory: 0.0395, power: 0.1700
Test Batch 1/4, reward: -34.901, revenue_rate: 0.3463, efficiency: 0.1415, distance: 6.3173, memory: 0.1295, power: 0.1957
Test Batch 2/4, reward: -45.697, revenue_rate: 0.3076, efficiency: 0.1104, distance: 5.4066, memory: 0.0529, power: 0.1781
Test Batch 3/4, reward: -65.893, revenue_rate: 0.3109, efficiency: 0.1088, distance: 5.2685, memory: 0.1018, power: 0.1548
Test Summary - Avg reward: -42.884±52.145, revenue_rate: 0.3198±0.0378, efficiency: 0.1191, completion_rate: 0.3710, distance: 5.6240, memory: 0.0751, power: 0.1802
Load Balance - Avg balance score: 0.5414±0.1810
Task Distribution by Satellite:
  Satellite 1: 962 tasks (26.55%)
  Satellite 2: 1790 tasks (49.39%)
  Satellite 3: 872 tasks (24.06%)
✅ 验证完成 - Epoch 1, reward: -42.884, revenue_rate: 0.3198, distance: 5.6240, memory: 0.0751, power: 0.1802
  ✅ 训练验证差距正常: -1.2830
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_06_10_38_39 (验证集奖励: -42.8842)

开始训练 Epoch 2/3
Batch 0: Reward: -14.8946, Loss: 5573.4468, Revenue: 0.3210, LoadBalance: 0.6149, Tasks: [S0:412(28.6%), S1:662(46.0%), S2:366(25.4%)], ActorGrad: 6573.5024, CriticGrad: 417.8150, Advantage: μ=-10.409, σ=59.783, range=[-81.92, 139.94]
Batch 5: Reward: -6.7082, Loss: 4487.0957, Revenue: 0.2736, LoadBalance: 0.7223, Tasks: [S0:445(35.7%), S1:423(33.9%), S2:380(30.4%)], ActorGrad: 6638.5083, CriticGrad: 147.2989, Advantage: μ=-3.115, σ=55.062, range=[-46.13, 139.73]
Epoch 2, Batch 10/32, loss: 4187.371↓, reward: -12.275↓, critic_reward: -2.633, revenue_rate: 0.2918, distance: 5.1893, memory: 0.0323, power: 0.2053, lr: 0.000400, took: 43.015s
Batch 10: Reward: 0.9249, Loss: 5953.9551, Revenue: 0.2911, LoadBalance: 0.6738, Tasks: [S0:436(32.4%), S1:351(26.1%), S2:557(41.4%)], ActorGrad: 7038.1035, CriticGrad: 185.6223, Advantage: μ=3.794, σ=62.273, range=[-54.44, 138.07]
Batch 15: Reward: -9.1716, Loss: 4501.3037, Revenue: 0.2856, LoadBalance: 0.6870, Tasks: [S0:442(32.9%), S1:563(41.9%), S2:339(25.2%)], ActorGrad: 6481.4536, CriticGrad: 145.1546, Advantage: μ=-4.351, σ=55.063, range=[-77.02, 139.94]
Epoch 2, Batch 20/32, loss: 4721.695↑, reward: -4.168↑, critic_reward: -3.858, revenue_rate: 0.2756, distance: 4.8741, memory: 0.0344, power: 0.1982, lr: 0.000400, took: 36.736s
Batch 20: Reward: 1.8111, Loss: 5021.4927, Revenue: 0.2534, LoadBalance: 0.7531, Tasks: [S0:402(34.9%), S1:345(29.9%), S2:405(35.2%)], ActorGrad: 8318.8672, CriticGrad: 154.4967, Advantage: μ=4.603, σ=57.733, range=[-57.26, 136.59]
Batch 25: Reward: -1.0767, Loss: 4466.0688, Revenue: 0.2499, LoadBalance: 0.7692, Tasks: [S0:389(33.8%), S1:374(32.5%), S2:389(33.8%)], ActorGrad: 4097.2632, CriticGrad: 170.5116, Advantage: μ=2.104, σ=54.993, range=[-33.32, 169.07]
Epoch 2, Batch 30/32, loss: 6046.508↓, reward: 9.732↓, critic_reward: -3.567, revenue_rate: 0.2856, distance: 4.9600, memory: 0.0536, power: 0.1979, lr: 0.000400, took: 36.917s
Batch 30: Reward: -0.1228, Loss: 5473.8965, Revenue: 0.2361, LoadBalance: 0.7208, Tasks: [S0:327(31.0%), S1:380(36.0%), S2:349(33.0%)], ActorGrad: 5058.2109, CriticGrad: 136.1041, Advantage: μ=2.186, σ=60.083, range=[-49.45, 167.05]

📊 Epoch 2 训练统计:
  平均奖励: -2.2482
  平均损失: 4937.4082
  平均收益率: 0.2796
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: 34.561, revenue_rate: 0.2792, efficiency: 0.0891, distance: 4.6448, memory: 0.0311, power: 0.1541
Test Batch 1/4, reward: -19.998, revenue_rate: 0.2745, efficiency: 0.0848, distance: 4.5428, memory: 0.0764, power: 0.1494
Test Batch 2/4, reward: -24.459, revenue_rate: 0.2852, efficiency: 0.0911, distance: 4.7226, memory: 0.0454, power: 0.1594
Test Batch 3/4, reward: -45.268, revenue_rate: 0.2277, efficiency: 0.0638, distance: 3.9169, memory: 0.1319, power: 0.1361
Test Summary - Avg reward: -4.978±66.494, revenue_rate: 0.2776±0.0389, efficiency: 0.0873, completion_rate: 0.3143, distance: 4.6079, memory: 0.0542, power: 0.1536
Load Balance - Avg balance score: 0.7255±0.1374
Task Distribution by Satellite:
  Satellite 1: 1019 tasks (33.39%)
  Satellite 2: 1093 tasks (35.81%)
  Satellite 3: 940 tasks (30.80%)
✅ 验证完成 - Epoch 2, reward: -4.978, revenue_rate: 0.2776, distance: 4.6079, memory: 0.0542, power: 0.1536
  ⚠️ 过拟合: 训练验证差距 = 2.7294
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_06_10_38_39 (验证集奖励: -4.9775)

开始训练 Epoch 3/3
Batch 0: Reward: 33.6746, Loss: 9409.5537, Revenue: 0.2334, LoadBalance: 0.7392, Tasks: [S0:353(32.4%), S1:419(38.5%), S2:316(29.0%)], ActorGrad: 5149.1235, CriticGrad: 672.5698, Advantage: μ=28.366, σ=71.484, range=[-51.30, 142.13]
Batch 5: Reward: -11.9721, Loss: 2700.8972, Revenue: 0.2607, LoadBalance: 0.7366, Tasks: [S0:379(31.2%), S1:418(34.4%), S2:419(34.5%)], ActorGrad: 3510.4268, CriticGrad: 215.8051, Advantage: μ=-8.993, σ=43.678, range=[-35.13, 130.41]
Epoch 3, Batch 10/32, loss: 5675.436↓, reward: 4.875↓, critic_reward: -1.710, revenue_rate: 0.2833, distance: 4.9815, memory: 0.0541, power: 0.1988, lr: 0.000400, took: 41.975s
Batch 10: Reward: 6.7117, Loss: 6143.5215, Revenue: 0.2687, LoadBalance: 0.7078, Tasks: [S0:500(41.1%), S1:338(27.8%), S2:378(31.1%)], ActorGrad: 5521.2866, CriticGrad: 229.9975, Advantage: μ=6.209, σ=62.967, range=[-38.99, 145.02]
Batch 15: Reward: 29.5209, Loss: 9278.5840, Revenue: 0.2718, LoadBalance: 0.7906, Tasks: [S0:397(31.8%), S1:449(36.0%), S2:402(32.2%)], ActorGrad: 5891.2876, CriticGrad: 698.1437, Advantage: μ=22.701, σ=72.808, range=[-36.72, 160.83]
Epoch 3, Batch 20/32, loss: 6292.010↓, reward: 12.923↓, critic_reward: 0.654, revenue_rate: 0.2704, distance: 4.6694, memory: 0.0441, power: 0.1917, lr: 0.000400, took: 35.711s
Batch 20: Reward: 18.2229, Loss: 7154.6909, Revenue: 0.3071, LoadBalance: 0.7682, Tasks: [S0:436(32.4%), S1:454(33.8%), S2:454(33.8%)], ActorGrad: 6907.3237, CriticGrad: 310.4520, Advantage: μ=11.929, σ=66.688, range=[-39.12, 160.01]
Batch 25: Reward: -35.2613, Loss: 1607.9470, Revenue: 0.3270, LoadBalance: 0.6538, Tasks: [S0:485(34.4%), S1:371(26.3%), S2:552(39.2%)], ActorGrad: 5153.0215, CriticGrad: 748.8558, Advantage: μ=-38.524, σ=11.307, range=[-65.33, -21.41]
Epoch 3, Batch 30/32, loss: 5510.096↓, reward: 4.254↓, critic_reward: 2.841, revenue_rate: 0.3024, distance: 5.2330, memory: 0.0399, power: 0.2017, lr: 0.000400, took: 39.747s
Batch 30: Reward: 16.5158, Loss: 7353.9858, Revenue: 0.2739, LoadBalance: 0.7141, Tasks: [S0:416(37.1%), S1:370(33.0%), S2:334(29.8%)], ActorGrad: 8522.4414, CriticGrad: 322.1799, Advantage: μ=11.300, σ=67.629, range=[-45.05, 138.83]

📊 Epoch 3 训练统计:
  平均奖励: 6.0508
  平均损失: 5759.9057
  平均收益率: 0.2834
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -86.129, revenue_rate: 0.3955, efficiency: 0.1855, distance: 7.1777, memory: 0.0522, power: 0.2326
Test Batch 1/4, reward: -70.974, revenue_rate: 0.4309, efficiency: 0.2315, distance: 8.4031, memory: 0.0737, power: 0.2672
Test Batch 2/4, reward: -74.006, revenue_rate: 0.3399, efficiency: 0.1356, distance: 5.9316, memory: 0.0805, power: 0.1958
Test Batch 3/4, reward: -71.606, revenue_rate: 0.3986, efficiency: 0.2064, distance: 7.2143, memory: 0.2612, power: 0.2525
Test Summary - Avg reward: -76.819±34.641, revenue_rate: 0.3892±0.0571, efficiency: 0.1851, completion_rate: 0.4704, distance: 7.1726, memory: 0.0765, power: 0.2327
Load Balance - Avg balance score: 0.3361±0.1505
Task Distribution by Satellite:
  Satellite 1: 518 tasks (11.21%)
  Satellite 2: 2471 tasks (53.48%)
  Satellite 3: 1631 tasks (35.30%)
✅ 验证完成 - Epoch 3, reward: -76.819, revenue_rate: 0.3892, distance: 7.1726, memory: 0.0765, power: 0.2327
  ⚠️ 过拟合: 训练验证差距 = 82.8700
训练完成

开始测试模型...
Test Batch 0/4, reward: -77.826, revenue_rate: 0.4829, efficiency: 0.2788, distance: 9.1485, memory: 0.0328, power: 0.2908
Test Batch 1/4, reward: -73.646, revenue_rate: 0.4474, efficiency: 0.2493, distance: 8.3566, memory: 0.1364, power: 0.2762
Test Batch 2/4, reward: -74.721, revenue_rate: 0.4246, efficiency: 0.2113, distance: 7.6549, memory: 0.0620, power: 0.2412
Test Batch 3/4, reward: -45.050, revenue_rate: 0.2729, efficiency: 0.0819, distance: 4.8604, memory: 0.0627, power: 0.1332
Test Summary - Avg reward: -74.184±25.602, revenue_rate: 0.4445±0.0627, efficiency: 0.2399, completion_rate: 0.5342, distance: 8.2456, memory: 0.0765, power: 0.2639
Load Balance - Avg balance score: 0.3483±0.1337
Task Distribution by Satellite:
  Satellite 1: 591 tasks (11.22%)
  Satellite 2: 2775 tasks (52.68%)
  Satellite 3: 1902 tasks (36.10%)
测试完成 - 平均奖励: -74.184, 平均星座收益率: 0.4445
✅ 模式 COMPETITIVE 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_competitive_2025_08_06_10_38_39
   平均奖励: -74.1835
   收益率: 0.4445

🚀 [3/3] 开始训练模式: HYBRID

============================================================
开始训练星座模式: HYBRID
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 32
seed: 12346
train-size: 1000
valid-size: 100
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: hybrid
verbose: True
2025_08_06_10_46_48
使用模型: gpn_transformer
Actor参数数量: 4,383,364
Critic参数数量: 2,152,321

开始训练 Epoch 1/3
Batch 0: Reward: -115.7532, Loss: 15383.2354, Revenue: 0.4165, LoadBalance: 0.3545, Tasks: [S0:965(52.9%), S1:213(11.7%), S2:646(35.4%)], ActorGrad: 43828.3477, CriticGrad: 7158.8804, Advantage: μ=-116.867, σ=42.202, range=[-237.54, -67.74]
Batch 5: Reward: -64.5988, Loss: 9019.0977, Revenue: 0.2580, LoadBalance: 0.5474, Tasks: [S0:224(18.9%), S1:426(36.0%), S2:534(45.1%)], ActorGrad: 12938.6943, CriticGrad: 960.1758, Advantage: μ=-48.858, σ=59.287, range=[-130.06, 243.99]
Epoch 1, Batch 10/32, loss: 16012.208↓, reward: -114.599↑, critic_reward: -2.912, revenue_rate: 0.4046, distance: 7.7497, memory: 0.0631, power: 0.2841, lr: 0.000400, took: 58.018s
Batch 10: Reward: -93.2528, Loss: 8361.3711, Revenue: 0.3712, LoadBalance: 0.4186, Tasks: [S0:609(38.8%), S1:755(48.2%), S2:204(13.0%)], ActorGrad: 16989.0566, CriticGrad: 1230.3793, Advantage: μ=-88.913, σ=21.693, range=[-150.27, -51.93]
Batch 15: Reward: -4.8751, Loss: 12400.9805, Revenue: 0.2796, LoadBalance: 0.7046, Tasks: [S0:442(37.3%), S1:394(33.3%), S2:348(29.4%)], ActorGrad: 12939.2900, CriticGrad: 249.4599, Advantage: μ=-0.273, σ=86.699, range=[-67.44, 233.06]
Epoch 1, Batch 20/32, loss: 12060.995↑, reward: -18.630↑, critic_reward: -4.422, revenue_rate: 0.2971, distance: 5.1269, memory: 0.0321, power: 0.1990, lr: 0.000400, took: 36.991s
Batch 20: Reward: -33.8696, Loss: 4978.4976, Revenue: 0.2793, LoadBalance: 0.7334, Tasks: [S0:416(32.5%), S1:512(40.0%), S2:352(27.5%)], ActorGrad: 9513.5635, CriticGrad: 552.1158, Advantage: μ=-24.497, σ=52.908, range=[-94.03, 253.31]
Batch 25: Reward: 35.2939, Loss: 21702.5566, Revenue: 0.2819, LoadBalance: 0.7320, Tasks: [S0:485(37.9%), S1:445(34.8%), S2:350(27.3%)], ActorGrad: 16339.8691, CriticGrad: 766.4854, Advantage: μ=29.097, σ=108.593, range=[-58.54, 232.02]
Epoch 1, Batch 30/32, loss: 15939.903↑, reward: 15.252↑, critic_reward: -3.159, revenue_rate: 0.2863, distance: 4.9585, memory: 0.0601, power: 0.1999, lr: 0.000400, took: 36.734s
Batch 30: Reward: 2.9013, Loss: 14908.4014, Revenue: 0.2755, LoadBalance: 0.7124, Tasks: [S0:470(38.7%), S1:407(33.5%), S2:339(27.9%)], ActorGrad: 15971.9385, CriticGrad: 361.4443, Advantage: μ=2.561, σ=94.305, range=[-134.57, 197.71]

📊 Epoch 1 训练统计:
  平均奖励: -38.4140
  平均损失: 14317.9458
  平均收益率: 0.3254
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -90.040, revenue_rate: 0.3626, efficiency: 0.1516, distance: 6.4118, memory: 0.0396, power: 0.2067
Test Batch 1/4, reward: -107.951, revenue_rate: 0.3184, efficiency: 0.1206, distance: 5.8081, memory: 0.0942, power: 0.1778
Test Batch 2/4, reward: -78.378, revenue_rate: 0.3572, efficiency: 0.1491, distance: 6.3518, memory: 0.0487, power: 0.2058
Test Batch 3/4, reward: 5.097, revenue_rate: 0.2193, efficiency: 0.0614, distance: 4.0585, memory: -0.0175, power: 0.1263
Test Summary - Avg reward: -88.234±92.292, revenue_rate: 0.3410±0.0546, efficiency: 0.1373, completion_rate: 0.3997, distance: 6.1053, memory: 0.0577, power: 0.1939
Load Balance - Avg balance score: 0.4904±0.2152
Task Distribution by Satellite:
  Satellite 1: 909 tasks (23.21%)
  Satellite 2: 2041 tasks (52.12%)
  Satellite 3: 966 tasks (24.67%)
✅ 验证完成 - Epoch 1, reward: -88.234, revenue_rate: 0.3410, distance: 6.1053, memory: 0.0577, power: 0.1939
  ⚠️ 过拟合: 训练验证差距 = 49.8202
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_06_10_46_48 (验证集奖励: -88.2342)

开始训练 Epoch 2/3
Batch 0: Reward: -5.7051, Loss: 16263.4121, Revenue: 0.2830, LoadBalance: 0.6422, Tasks: [S0:323(26.6%), S1:544(44.7%), S2:349(28.7%)], ActorGrad: 17285.7539, CriticGrad: 315.2573, Advantage: μ=-4.530, σ=98.098, range=[-78.01, 229.71]
Batch 5: Reward: -64.5021, Loss: 4694.3076, Revenue: 0.3625, LoadBalance: 0.6146, Tasks: [S0:728(44.6%), S1:348(21.3%), S2:556(34.1%)], ActorGrad: 10796.6387, CriticGrad: 1210.9835, Advantage: μ=-63.730, σ=25.558, range=[-138.53, -28.64]
Epoch 2, Batch 10/32, loss: 9978.058↓, reward: -37.152↓, critic_reward: -0.651, revenue_rate: 0.3059, distance: 5.3794, memory: 0.0453, power: 0.2172, lr: 0.000400, took: 43.380s
Batch 10: Reward: -2.6959, Loss: 12229.1641, Revenue: 0.3018, LoadBalance: 0.7304, Tasks: [S0:420(31.2%), S1:411(30.6%), S2:513(38.2%)], ActorGrad: 13321.0283, CriticGrad: 172.2986, Advantage: μ=-0.493, σ=86.147, range=[-98.71, 219.35]
Batch 15: Reward: -3.8706, Loss: 13419.5332, Revenue: 0.2709, LoadBalance: 0.7375, Tasks: [S0:464(37.2%), S1:372(29.8%), S2:412(33.0%)], ActorGrad: 13765.1885, CriticGrad: 250.6833, Advantage: μ=-0.611, σ=89.885, range=[-78.56, 265.70]
Epoch 2, Batch 20/32, loss: 10429.565↑, reward: -30.753↑, critic_reward: -3.043, revenue_rate: 0.2999, distance: 5.3054, memory: 0.0522, power: 0.2126, lr: 0.000400, took: 38.703s
Batch 20: Reward: -66.4226, Loss: 4264.2725, Revenue: 0.3265, LoadBalance: 0.5784, Tasks: [S0:650(44.2%), S1:287(19.5%), S2:535(36.3%)], ActorGrad: 11005.1836, CriticGrad: 937.1688, Advantage: μ=-62.553, σ=19.044, range=[-92.80, -21.82]
Batch 25: Reward: 20.2528, Loss: 16513.5273, Revenue: 0.2605, LoadBalance: 0.7527, Tasks: [S0:356(30.1%), S1:451(38.1%), S2:377(31.8%)], ActorGrad: 14454.6328, CriticGrad: 449.2339, Advantage: μ=18.787, σ=97.179, range=[-52.52, 231.79]
Epoch 2, Batch 30/32, loss: 8309.808↑, reward: -49.062↑, critic_reward: -4.290, revenue_rate: 0.3044, distance: 5.5142, memory: 0.0726, power: 0.2217, lr: 0.000400, took: 40.290s
Batch 30: Reward: -19.7711, Loss: 10949.7295, Revenue: 0.3238, LoadBalance: 0.7045, Tasks: [S0:373(25.3%), S1:496(33.7%), S2:603(41.0%)], ActorGrad: 10910.1719, CriticGrad: 284.0114, Advantage: μ=-11.558, σ=81.154, range=[-78.03, 242.23]

📊 Epoch 2 训练统计:
  平均奖励: -37.3107
  平均损失: 9587.8554
  平均收益率: 0.3009
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -19.550, revenue_rate: 0.3446, efficiency: 0.1411, distance: 5.8143, memory: 0.0820, power: 0.2052
Test Batch 1/4, reward: -5.025, revenue_rate: 0.3306, efficiency: 0.1186, distance: 5.5901, memory: 0.0244, power: 0.1694
Test Batch 2/4, reward: -35.812, revenue_rate: 0.2918, efficiency: 0.1015, distance: 5.2355, memory: 0.0698, power: 0.1699
Test Batch 3/4, reward: 80.538, revenue_rate: 0.2908, efficiency: 0.1011, distance: 5.0937, memory: 0.0966, power: 0.1582
Test Summary - Avg reward: -16.102±122.303, revenue_rate: 0.3211±0.0437, efficiency: 0.1196, completion_rate: 0.3711, distance: 5.5285, memory: 0.0602, power: 0.1806
Load Balance - Avg balance score: 0.6458±0.1755
Task Distribution by Satellite:
  Satellite 1: 1569 tasks (43.29%)
  Satellite 2: 1108 tasks (30.57%)
  Satellite 3: 947 tasks (26.13%)
✅ 验证完成 - Epoch 2, reward: -16.102, revenue_rate: 0.3211, distance: 5.5285, memory: 0.0602, power: 0.1806
  ⚠️ 欠拟合: 训练验证差距 = -21.2084
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_06_10_46_48 (验证集奖励: -16.1023)

开始训练 Epoch 3/3
Batch 0: Reward: -8.8093, Loss: 13038.2549, Revenue: 0.2849, LoadBalance: 0.6775, Tasks: [S0:552(42.1%), S1:367(28.0%), S2:393(30.0%)], ActorGrad: 14677.5361, CriticGrad: 198.5195, Advantage: μ=-2.923, σ=88.663, range=[-69.74, 207.22]
Batch 5: Reward: -19.5850, Loss: 8538.8809, Revenue: 0.2893, LoadBalance: 0.7198, Tasks: [S0:367(28.0%), S1:455(34.7%), S2:490(37.3%)], ActorGrad: 9433.5596, CriticGrad: 279.0794, Advantage: μ=-11.313, σ=72.407, range=[-94.76, 210.17]
Epoch 3, Batch 10/32, loss: 13471.715↑, reward: -6.617↑, critic_reward: -5.095, revenue_rate: 0.2709, distance: 4.7305, memory: 0.0621, power: 0.1966, lr: 0.000400, took: 40.699s
Batch 10: Reward: -6.9590, Loss: 11188.9863, Revenue: 0.2971, LoadBalance: 0.7396, Tasks: [S0:524(38.1%), S1:430(31.2%), S2:422(30.7%)], ActorGrad: 11748.8193, CriticGrad: 224.1421, Advantage: μ=-1.380, σ=82.719, range=[-73.56, 239.98]
Batch 15: Reward: 9.4667, Loss: 16800.8750, Revenue: 0.3328, LoadBalance: 0.7243, Tasks: [S0:434(29.5%), S1:552(37.5%), S2:486(33.0%)], ActorGrad: 13901.0615, CriticGrad: 346.0611, Advantage: μ=10.906, σ=99.114, range=[-60.62, 238.40]
Epoch 3, Batch 20/32, loss: 14188.059↓, reward: 1.955↓, critic_reward: -4.934, revenue_rate: 0.2875, distance: 4.9583, memory: 0.0264, power: 0.2045, lr: 0.000400, took: 38.357s
Batch 20: Reward: -4.2187, Loss: 12169.8086, Revenue: 0.2614, LoadBalance: 0.7056, Tasks: [S0:418(35.3%), S1:334(28.2%), S2:432(36.5%)], ActorGrad: 11868.4727, CriticGrad: 243.8073, Advantage: μ=0.178, σ=85.957, range=[-70.29, 234.56]
Batch 25: Reward: 40.4629, Loss: 23631.2227, Revenue: 0.2808, LoadBalance: 0.7620, Tasks: [S0:439(34.3%), S1:395(30.9%), S2:446(34.8%)], ActorGrad: 14451.9248, CriticGrad: 931.4744, Advantage: μ=32.735, σ=112.331, range=[-124.97, 231.86]
Epoch 3, Batch 30/32, loss: 18999.435↑, reward: 24.550↑, critic_reward: -3.146, revenue_rate: 0.2722, distance: 4.7539, memory: 0.0527, power: 0.1969, lr: 0.000400, took: 37.216s
Batch 30: Reward: 47.1221, Loss: 20802.1094, Revenue: 0.2609, LoadBalance: 0.7743, Tasks: [S0:377(31.0%), S1:420(34.5%), S2:419(34.5%)], ActorGrad: 15385.3662, CriticGrad: 1175.7332, Advantage: μ=35.554, σ=104.441, range=[-60.99, 227.39]

📊 Epoch 3 训练统计:
  平均奖励: 6.2598
  平均损失: 15307.3374
  平均收益率: 0.2757
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 0/4, reward: -30.289, revenue_rate: 0.2946, efficiency: 0.0969, distance: 5.0388, memory: 0.0506, power: 0.1624
Test Batch 1/4, reward: -20.262, revenue_rate: 0.3457, efficiency: 0.1378, distance: 5.8651, memory: 0.0983, power: 0.1887
Test Batch 2/4, reward: -36.677, revenue_rate: 0.3021, efficiency: 0.1024, distance: 5.0364, memory: 0.0204, power: 0.1664
Test Batch 3/4, reward: 32.082, revenue_rate: 0.3578, efficiency: 0.1488, distance: 5.9333, memory: -0.0234, power: 0.1942
Test Summary - Avg reward: -26.630±99.123, revenue_rate: 0.3159±0.0450, efficiency: 0.1139, completion_rate: 0.3580, distance: 5.3382, memory: 0.0533, power: 0.1734
Load Balance - Avg balance score: 0.6786±0.1667
Task Distribution by Satellite:
  Satellite 1: 1401 tasks (40.12%)
  Satellite 2: 977 tasks (27.98%)
  Satellite 3: 1114 tasks (31.90%)
✅ 验证完成 - Epoch 3, reward: -26.630, revenue_rate: 0.3159, distance: 5.3382, memory: 0.0533, power: 0.1734
  ⚠️ 过拟合: 训练验证差距 = 32.8894
训练完成

开始测试模型...
Test Batch 0/4, reward: 26.128, revenue_rate: 0.3272, efficiency: 0.1177, distance: 5.3489, memory: 0.0347, power: 0.1813
Test Batch 1/4, reward: -12.467, revenue_rate: 0.3530, efficiency: 0.1505, distance: 6.4723, memory: 0.0823, power: 0.2103
Test Batch 2/4, reward: -10.917, revenue_rate: 0.3422, efficiency: 0.1364, distance: 5.7768, memory: 0.0986, power: 0.1943
Test Batch 3/4, reward: 32.018, revenue_rate: 0.2416, efficiency: 0.0652, distance: 3.6980, memory: -0.0246, power: 0.1289
Test Summary - Avg reward: 2.159±127.204, revenue_rate: 0.3369±0.0453, efficiency: 0.1321, completion_rate: 0.3898, distance: 5.7793, memory: 0.0680, power: 0.1927
Load Balance - Avg balance score: 0.7000±0.1615
Task Distribution by Satellite:
  Satellite 1: 1556 tasks (40.78%)
  Satellite 2: 1022 tasks (26.78%)
  Satellite 3: 1238 tasks (32.44%)
测试完成 - 平均奖励: 2.159, 平均星座收益率: 0.3369
✅ 模式 HYBRID 训练完成
   保存路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_hybrid_2025_08_06_10_46_48
   平均奖励: 2.1587
   收益率: 0.3369

================================================================================
🎯 多模式训练总结
================================================================================
✅ COOPERATIVE: 奖励=17.7168, 收益率=0.2977
✅ COMPETITIVE: 奖励=-74.1835, 收益率=0.4445
✅ HYBRID: 奖励=2.1587, 收益率=0.3369

🏆 最佳模式: COOPERATIVE
   最高奖励: 17.7168
   对应收益率: 0.2977
   模型路径: constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_06_10_30_29

🎉 所有模式训练完成！
