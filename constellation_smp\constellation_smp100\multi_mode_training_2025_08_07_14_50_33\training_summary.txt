🛰️  开始多模式星座任务规划训练
将依次训练以下模式: COOPERATIVE, COMPETITIVE, HYBRID
训练配置: all
主日志目录: constellation_smp\constellation_smp100\multi_mode_training_2025_08_07_14_50_33
================================================================================

🚀 [1/3] 开始训练模式: COOPERATIVE

============================================================
开始训练星座模式: COOPERATIVE
============================================================
constellation_smp: 100
model: gpn_transformer
rnn: indrnn
hidden_size: 128
batch_size: 64
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 0.0004
critic_lr: 0.0004
num_satellites: 3
constellation_mode: cooperative
verbose: True
2025_08_07_14_56_50
使用模型: gpn_transformer
Actor参数数量: 903,876
Critic参数数量: 343,361

开始训练 Epoch 1/3
Batch 0: Reward: -1.1588, Loss: 510.7267, Revenue: 0.3084, LoadBalance: 0.6816, Tasks: [S0:863(34.6%), S1:656(26.3%), S2:977(39.1%)], ActorGrad: 2587.2981, CriticGrad: 69.2507, Advantage: μ=-0.643, σ=22.769, range=[-17.07, 64.90]
Epoch 1, Batch 50/1563, loss: 679.525↓, reward: 3.004↓, critic_reward: 1.257, revenue_rate: 0.3034, distance: 5.4050, memory: 0.0584, power: 0.1980, lr: 0.000400, took: 361.450s
Batch 50: Reward: 2.9722, Loss: 681.7471, Revenue: 0.3079, LoadBalance: 0.7280, Tasks: [S0:689(27.6%), S1:967(38.7%), S2:840(33.7%)], ActorGrad: 1361.6249, CriticGrad: 35.1189, Advantage: μ=0.828, σ=26.303, range=[-15.85, 64.02]
Epoch 1, Batch 100/1563, loss: 722.928↑, reward: 4.414↑, critic_reward: 2.450, revenue_rate: 0.3054, distance: 5.3886, memory: 0.0586, power: 0.2031, lr: 0.000400, took: 349.338s
Batch 100: Reward: 1.3773, Loss: 567.4844, Revenue: 0.2899, LoadBalance: 0.7482, Tasks: [S0:888(35.6%), S1:758(30.4%), S2:850(34.1%)], ActorGrad: 1121.2683, CriticGrad: 27.0773, Advantage: μ=-1.458, σ=23.965, range=[-14.42, 63.81]
Epoch 1, Batch 150/1563, loss: 723.709↑, reward: 4.924↑, critic_reward: 3.166, revenue_rate: 0.2883, distance: 5.0550, memory: 0.0644, power: 0.1969, lr: 0.000400, took: 331.024s
Batch 150: Reward: 9.6083, Loss: 959.0219, Revenue: 0.3435, LoadBalance: 0.7667, Tasks: [S0:998(33.2%), S1:952(31.6%), S2:1058(35.2%)], ActorGrad: 1581.4771, CriticGrad: 77.5943, Advantage: μ=6.107, σ=30.600, range=[-18.38, 65.74]
Epoch 1, Batch 200/1563, loss: 763.547↓, reward: 5.907↓, critic_reward: 3.884, revenue_rate: 0.2929, distance: 5.1410, memory: 0.0604, power: 0.2042, lr: 0.000400, took: 353.674s
Batch 200: Reward: 4.2413, Loss: 654.3486, Revenue: 0.2870, LoadBalance: 0.7619, Tasks: [S0:904(34.5%), S1:865(33.0%), S2:855(32.6%)], ActorGrad: 1394.3878, CriticGrad: 22.6624, Advantage: μ=0.115, σ=25.782, range=[-18.18, 64.44]
Epoch 1, Batch 250/1563, loss: 798.158↑, reward: 6.709↑, critic_reward: 4.220, revenue_rate: 0.2961, distance: 5.2502, memory: 0.0600, power: 0.2036, lr: 0.000400, took: 350.435s
Batch 250: Reward: 4.8884, Loss: 719.1833, Revenue: 0.2617, LoadBalance: 0.7295, Tasks: [S0:803(36.9%), S1:706(32.4%), S2:667(30.7%)], ActorGrad: 1550.0706, CriticGrad: 25.6565, Advantage: μ=0.523, σ=27.024, range=[-20.48, 64.49]
Epoch 1, Batch 300/1563, loss: 755.978↑, reward: 5.572↑, critic_reward: 4.345, revenue_rate: 0.3044, distance: 5.4180, memory: 0.0589, power: 0.2060, lr: 0.000400, took: 357.243s
Batch 300: Reward: 2.0616, Loss: 593.6787, Revenue: 0.2923, LoadBalance: 0.7307, Tasks: [S0:772(30.2%), S1:843(32.9%), S2:945(36.9%)], ActorGrad: 1227.1595, CriticGrad: 45.0513, Advantage: μ=-2.086, σ=24.468, range=[-20.49, 62.29]
Epoch 1, Batch 350/1563, loss: 815.169↑, reward: 7.138↑, critic_reward: 4.493, revenue_rate: 0.2987, distance: 5.2695, memory: 0.0662, power: 0.2041, lr: 0.000400, took: 346.345s
Batch 350: Reward: 10.3624, Loss: 931.0787, Revenue: 0.3409, LoadBalance: 0.7793, Tasks: [S0:1030(34.2%), S1:956(31.8%), S2:1022(34.0%)], ActorGrad: 1699.2157, CriticGrad: 67.9482, Advantage: μ=5.465, σ=30.258, range=[-18.43, 63.93]
Epoch 1, Batch 400/1563, loss: 764.987↓, reward: 6.227↓, critic_reward: 4.781, revenue_rate: 0.2887, distance: 5.0628, memory: 0.0621, power: 0.1999, lr: 0.000400, took: 337.262s
Batch 400: Reward: 8.4514, Loss: 900.5054, Revenue: 0.2867, LoadBalance: 0.7429, Tasks: [S0:901(34.3%), S1:830(31.6%), S2:893(34.0%)], ActorGrad: 1495.4811, CriticGrad: 54.4034, Advantage: μ=3.465, σ=30.043, range=[-18.47, 66.88]
Epoch 1, Batch 450/1563, loss: 827.069↓, reward: 8.020↓, critic_reward: 5.072, revenue_rate: 0.2941, distance: 5.1532, memory: 0.0575, power: 0.2068, lr: 0.000400, took: 358.935s
Batch 450: Reward: 4.7839, Loss: 660.4360, Revenue: 0.2757, LoadBalance: 0.7436, Tasks: [S0:837(34.4%), S1:814(33.5%), S2:781(32.1%)], ActorGrad: 914.4154, CriticGrad: 32.2441, Advantage: μ=-0.592, σ=25.895, range=[-19.15, 62.26]
Epoch 1, Batch 500/1563, loss: 726.198↑, reward: 5.227↑, critic_reward: 5.092, revenue_rate: 0.2906, distance: 5.1153, memory: 0.0611, power: 0.2029, lr: 0.000400, took: 347.722s
Batch 500: Reward: 10.1465, Loss: 942.0370, Revenue: 0.2821, LoadBalance: 0.7390, Tasks: [S0:1009(37.5%), S1:819(30.5%), S2:860(32.0%)], ActorGrad: 1535.7291, CriticGrad: 75.0708, Advantage: μ=5.346, σ=30.462, range=[-18.01, 72.86]
Epoch 1, Batch 550/1563, loss: 801.895↑, reward: 7.340↑, critic_reward: 5.082, revenue_rate: 0.2956, distance: 5.2600, memory: 0.0659, power: 0.2103, lr: 0.000400, took: 362.790s
Batch 550: Reward: 5.6438, Loss: 764.8545, Revenue: 0.2730, LoadBalance: 0.7366, Tasks: [S0:832(34.2%), S1:775(31.9%), S2:825(33.9%)], ActorGrad: 1092.3379, CriticGrad: 24.1825, Advantage: μ=0.395, σ=27.872, range=[-18.00, 64.58]
Epoch 1, Batch 600/1563, loss: 787.209↓, reward: 6.743↓, critic_reward: 5.462, revenue_rate: 0.2956, distance: 5.2837, memory: 0.0688, power: 0.2133, lr: 0.000400, took: 365.465s
Batch 600: Reward: 3.8732, Loss: 676.6338, Revenue: 0.2808, LoadBalance: 0.7297, Tasks: [S0:931(35.5%), S1:874(33.3%), S2:819(31.2%)], ActorGrad: 1282.9100, CriticGrad: 27.3659, Advantage: μ=-1.445, σ=26.177, range=[-22.18, 65.61]
Epoch 1, Batch 650/1563, loss: 764.183↑, reward: 6.214↑, critic_reward: 5.358, revenue_rate: 0.2954, distance: 5.2362, memory: 0.0672, power: 0.2142, lr: 0.000400, took: 368.746s
Batch 650: Reward: 3.7429, Loss: 686.7130, Revenue: 0.3343, LoadBalance: 0.7529, Tasks: [S0:1007(31.5%), S1:1154(36.1%), S2:1039(32.5%)], ActorGrad: 1377.4746, CriticGrad: 29.3904, Advantage: μ=-1.586, σ=26.364, range=[-21.37, 64.45]
Epoch 1, Batch 700/1563, loss: 791.803↑, reward: 7.344↑, critic_reward: 5.594, revenue_rate: 0.2959, distance: 5.2627, memory: 0.0599, power: 0.2160, lr: 0.000400, took: 368.973s
Batch 700: Reward: 8.5407, Loss: 858.7740, Revenue: 0.3056, LoadBalance: 0.7628, Tasks: [S0:952(33.8%), S1:934(33.2%), S2:930(33.0%)], ActorGrad: 1398.8761, CriticGrad: 44.6877, Advantage: μ=2.789, σ=29.402, range=[-18.00, 61.21]
Epoch 1, Batch 750/1563, loss: 824.557↓, reward: 7.899↓, critic_reward: 5.836, revenue_rate: 0.2943, distance: 5.2557, memory: 0.0588, power: 0.2152, lr: 0.000400, took: 369.617s
Batch 750: Reward: 5.6022, Loss: 745.5151, Revenue: 0.3306, LoadBalance: 0.7662, Tasks: [S0:1175(36.7%), S1:996(31.1%), S2:1029(32.2%)], ActorGrad: 1460.7853, CriticGrad: 20.5720, Advantage: μ=-0.472, σ=27.516, range=[-21.80, 61.86]
Epoch 1, Batch 800/1563, loss: 809.425↑, reward: 7.578↑, critic_reward: 6.024, revenue_rate: 0.2932, distance: 5.2375, memory: 0.0696, power: 0.2160, lr: 0.000400, took: 370.165s
Batch 800: Reward: 5.8444, Loss: 688.8422, Revenue: 0.2712, LoadBalance: 0.7534, Tasks: [S0:810(30.9%), S1:943(35.9%), S2:871(33.2%)], ActorGrad: 1449.6334, CriticGrad: 23.3933, Advantage: μ=-0.232, σ=26.452, range=[-17.64, 60.89]
Epoch 1, Batch 850/1563, loss: 813.773↑, reward: 7.906↑, critic_reward: 6.189, revenue_rate: 0.2902, distance: 5.1501, memory: 0.0700, power: 0.2116, lr: 0.000400, took: 361.160s
Batch 850: Reward: 11.0659, Loss: 960.8499, Revenue: 0.3153, LoadBalance: 0.7800, Tasks: [S0:956(31.8%), S1:1047(34.8%), S2:1005(33.4%)], ActorGrad: 1483.4545, CriticGrad: 63.7889, Advantage: μ=4.894, σ=30.851, range=[-20.21, 64.98]
Epoch 1, Batch 900/1563, loss: 787.416↓, reward: 7.037↓, critic_reward: 6.272, revenue_rate: 0.2909, distance: 5.1627, memory: 0.0628, power: 0.2130, lr: 0.000400, took: 364.545s
Batch 900: Reward: -0.9665, Loss: 452.3378, Revenue: 0.2699, LoadBalance: 0.7546, Tasks: [S0:889(35.6%), S1:767(30.7%), S2:840(33.7%)], ActorGrad: 1105.9728, CriticGrad: 84.7210, Advantage: μ=-7.257, σ=20.150, range=[-19.61, 70.82]
Epoch 1, Batch 950/1563, loss: 795.843↑, reward: 7.460↑, critic_reward: 6.332, revenue_rate: 0.2855, distance: 5.0477, memory: 0.0654, power: 0.2104, lr: 0.000400, took: 360.061s
Batch 950: Reward: -0.8227, Loss: 422.4825, Revenue: 0.2665, LoadBalance: 0.7438, Tasks: [S0:872(34.1%), S1:864(33.8%), S2:824(32.2%)], ActorGrad: 1040.7128, CriticGrad: 90.9104, Advantage: μ=-7.052, σ=19.459, range=[-17.42, 55.80]
Epoch 1, Batch 1000/1563, loss: 811.809↑, reward: 7.893↑, critic_reward: 6.409, revenue_rate: 0.2932, distance: 5.2349, memory: 0.0659, power: 0.2150, lr: 0.000400, took: 368.392s
Batch 1000: Reward: 13.3555, Loss: 912.7066, Revenue: 0.2809, LoadBalance: 0.7744, Tasks: [S0:907(33.7%), S1:896(33.3%), S2:885(32.9%)], ActorGrad: 1506.4933, CriticGrad: 94.1434, Advantage: μ=7.039, σ=29.612, range=[-18.32, 68.31]
Epoch 1, Batch 1050/1563, loss: 821.675↓, reward: 8.128↓, critic_reward: 6.566, revenue_rate: 0.2917, distance: 5.1927, memory: 0.0604, power: 0.2133, lr: 0.000400, took: 365.243s
Batch 1050: Reward: 6.5886, Loss: 832.2216, Revenue: 0.2981, LoadBalance: 0.7585, Tasks: [S0:991(35.2%), S1:865(30.7%), S2:960(34.1%)], ActorGrad: 1468.8875, CriticGrad: 24.9629, Advantage: μ=-0.004, σ=29.076, range=[-20.63, 64.55]
Epoch 1, Batch 1100/1563, loss: 824.576↓, reward: 8.207↓, critic_reward: 6.659, revenue_rate: 0.2943, distance: 5.2670, memory: 0.0603, power: 0.2154, lr: 0.000400, took: 368.389s
Batch 1100: Reward: 7.7539, Loss: 812.0847, Revenue: 0.3011, LoadBalance: 0.7698, Tasks: [S0:970(35.2%), S1:833(30.3%), S2:949(34.5%)], ActorGrad: 1556.8939, CriticGrad: 31.5731, Advantage: μ=1.054, σ=28.703, range=[-20.96, 61.82]
Epoch 1, Batch 1150/1563, loss: 804.591↑, reward: 7.555↑, critic_reward: 6.760, revenue_rate: 0.2909, distance: 5.1420, memory: 0.0605, power: 0.2126, lr: 0.000400, took: 364.360s
Batch 1150: Reward: 7.1136, Loss: 745.8678, Revenue: 0.2765, LoadBalance: 0.7757, Tasks: [S0:877(33.4%), S1:855(32.6%), S2:892(34.0%)], ActorGrad: 1168.3105, CriticGrad: 29.4280, Advantage: μ=0.342, σ=27.524, range=[-19.05, 62.14]
Epoch 1, Batch 1200/1563, loss: 758.854↑, reward: 6.449↓, critic_reward: 6.673, revenue_rate: 0.2884, distance: 5.1358, memory: 0.0629, power: 0.2117, lr: 0.000400, took: 363.348s
Batch 1200: Reward: 5.7461, Loss: 698.8834, Revenue: 0.2810, LoadBalance: 0.7387, Tasks: [S0:975(36.3%), S1:791(29.4%), S2:922(34.3%)], ActorGrad: 1316.9460, CriticGrad: 23.4863, Advantage: μ=-0.617, σ=26.638, range=[-19.47, 59.47]
Epoch 1, Batch 1250/1563, loss: 789.809↑, reward: 7.281↑, critic_reward: 6.109, revenue_rate: 0.2952, distance: 5.2238, memory: 0.0586, power: 0.2164, lr: 0.000400, took: 369.778s
Batch 1250: Reward: 5.8329, Loss: 787.3735, Revenue: 0.2959, LoadBalance: 0.7765, Tasks: [S0:894(32.5%), S1:892(32.4%), S2:966(35.1%)], ActorGrad: 1335.4265, CriticGrad: 37.3557, Advantage: μ=-0.303, σ=28.280, range=[-19.70, 66.17]
Epoch 1, Batch 1300/1563, loss: 800.416↑, reward: 7.546↑, critic_reward: 6.578, revenue_rate: 0.2876, distance: 5.1130, memory: 0.0606, power: 0.2141, lr: 0.000400, took: 366.181s
Batch 1300: Reward: 5.0132, Loss: 695.3970, Revenue: 0.2613, LoadBalance: 0.7738, Tasks: [S0:889(35.6%), S1:775(31.0%), S2:832(33.3%)], ActorGrad: 1158.3037, CriticGrad: 24.6567, Advantage: μ=-1.623, σ=26.528, range=[-17.66, 71.94]
Epoch 1, Batch 1350/1563, loss: 811.255↑, reward: 7.835↑, critic_reward: 6.817, revenue_rate: 0.2915, distance: 5.1818, memory: 0.0593, power: 0.2168, lr: 0.000400, took: 369.211s
Batch 1350: Reward: 12.6889, Loss: 962.2561, Revenue: 0.3206, LoadBalance: 0.7744, Tasks: [S0:1016(33.8%), S1:920(30.6%), S2:1072(35.6%)], ActorGrad: 1840.4847, CriticGrad: 79.5190, Advantage: μ=5.628, σ=30.747, range=[-20.57, 64.07]
Epoch 1, Batch 1400/1563, loss: 834.418↓, reward: 8.350↓, critic_reward: 6.911, revenue_rate: 0.2882, distance: 5.1488, memory: 0.0606, power: 0.2156, lr: 0.000400, took: 368.312s
Batch 1400: Reward: 10.5570, Loss: 902.1055, Revenue: 0.2476, LoadBalance: 0.7551, Tasks: [S0:793(32.6%), S1:764(31.4%), S2:875(36.0%)], ActorGrad: 1439.6698, CriticGrad: 53.2606, Advantage: μ=3.740, σ=30.037, range=[-19.31, 63.60]
Epoch 1, Batch 1450/1563, loss: 796.580↓, reward: 7.332↓, critic_reward: 7.024, revenue_rate: 0.2949, distance: 5.2666, memory: 0.0630, power: 0.2193, lr: 0.000400, took: 376.121s
Batch 1450: Reward: 8.0995, Loss: 792.2158, Revenue: 0.2838, LoadBalance: 0.7488, Tasks: [S0:830(30.9%), S1:982(36.5%), S2:876(32.6%)], ActorGrad: 1358.0795, CriticGrad: 30.7025, Advantage: μ=1.466, σ=28.330, range=[-18.19, 70.08]
Epoch 1, Batch 1500/1563, loss: 801.565↓, reward: 7.560↓, critic_reward: 6.941, revenue_rate: 0.2883, distance: 5.1273, memory: 0.0602, power: 0.2141, lr: 0.000400, took: 367.786s
Batch 1500: Reward: 9.4278, Loss: 859.7065, Revenue: 0.2905, LoadBalance: 0.7735, Tasks: [S0:932(33.9%), S1:914(33.2%), S2:906(32.9%)], ActorGrad: 1782.9659, CriticGrad: 41.7966, Advantage: μ=2.317, σ=29.460, range=[-19.71, 63.10]
Epoch 1, Batch 1550/1563, loss: 853.656↓, reward: 8.885↓, critic_reward: 7.059, revenue_rate: 0.2875, distance: 5.1090, memory: 0.0644, power: 0.2140, lr: 0.000400, took: 368.647s
Batch 1550: Reward: 8.8544, Loss: 885.7488, Revenue: 0.2959, LoadBalance: 0.7555, Tasks: [S0:917(31.8%), S1:1012(35.1%), S2:951(33.0%)], ActorGrad: 1522.6753, CriticGrad: 30.5391, Advantage: μ=1.519, σ=29.958, range=[-20.66, 73.15]

📊 Epoch 1 训练统计:
  平均奖励: 6.9754
  平均损失: 789.6762
  平均收益率: 0.2932
  当前学习率: 0.000400

🔍 开始验证...
Test Batch 10/157, reward: 5.246, revenue_rate: 0.3826, efficiency: 0.1751, distance: 7.0894, memory: 0.0523, power: 0.2257
Test Batch 20/157, reward: 0.705, revenue_rate: 0.3249, efficiency: 0.1232, distance: 5.8071, memory: 0.0589, power: 0.1851
Test Batch 30/157, reward: 6.672, revenue_rate: 0.3335, efficiency: 0.1295, distance: 5.8202, memory: -0.0022, power: 0.1909
Test Batch 40/157, reward: -5.665, revenue_rate: 0.3229, efficiency: 0.1191, distance: 5.6110, memory: 0.0473, power: 0.1762
Test Batch 50/157, reward: 6.760, revenue_rate: 0.3107, efficiency: 0.1082, distance: 5.2846, memory: 0.0421, power: 0.1692
Test Batch 60/157, reward: -1.264, revenue_rate: 0.3325, efficiency: 0.1292, distance: 6.0318, memory: 0.0735, power: 0.1882
Test Batch 70/157, reward: -1.960, revenue_rate: 0.3016, efficiency: 0.1023, distance: 5.2532, memory: 0.0077, power: 0.1638
Test Batch 80/157, reward: 2.018, revenue_rate: 0.3293, efficiency: 0.1248, distance: 5.8093, memory: 0.0531, power: 0.1848
Test Batch 90/157, reward: 5.710, revenue_rate: 0.3256, efficiency: 0.1234, distance: 5.6539, memory: 0.0521, power: 0.1862
Test Batch 100/157, reward: -1.074, revenue_rate: 0.3319, efficiency: 0.1226, distance: 5.6665, memory: 0.0119, power: 0.1787
Test Batch 110/157, reward: 4.734, revenue_rate: 0.3435, efficiency: 0.1402, distance: 6.0485, memory: 0.0765, power: 0.2039
Test Batch 120/157, reward: 4.079, revenue_rate: 0.3889, efficiency: 0.1819, distance: 7.1469, memory: 0.0134, power: 0.2300
Test Batch 130/157, reward: 2.530, revenue_rate: 0.3699, efficiency: 0.1625, distance: 6.7219, memory: 0.0536, power: 0.2109
Test Batch 140/157, reward: 1.131, revenue_rate: 0.3146, efficiency: 0.1161, distance: 5.7078, memory: 0.0745, power: 0.1829
Test Batch 150/157, reward: 4.732, revenue_rate: 0.3406, efficiency: 0.1325, distance: 5.9877, memory: 0.0341, power: 0.1842
Test Batch 157/157, reward: -3.130, revenue_rate: 0.2693, efficiency: 0.0862, distance: 4.4097, memory: 0.0024, power: 0.1573
Test Summary - Avg reward: 3.228±26.614, revenue_rate: 0.3376±0.0449, efficiency: 0.1325, completion_rate: 0.3907, distance: 5.9153, memory: 0.0410, power: 0.1904
Load Balance - Avg balance score: 0.7367±0.1354
Task Distribution by Satellite:
  Satellite 1: 123534 tasks (32.33%)
  Satellite 2: 141350 tasks (37.00%)
  Satellite 3: 117180 tasks (30.67%)
✅ 验证完成 - Epoch 1, reward: 3.228, revenue_rate: 0.3376, distance: 5.9153, memory: 0.0410, power: 0.1904
  ⚠️ 过拟合: 训练验证差距 = 3.7474
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpn_transformerindrnn_cooperative_2025_08_07_14_56_50 (验证集奖励: 3.2280)

开始训练 Epoch 2/3
Batch 0: Reward: 16.4225, Loss: 1141.2499, Revenue: 0.2913, LoadBalance: 0.7891, Tasks: [S0:949(33.0%), S1:1036(36.0%), S2:895(31.1%)], ActorGrad: 1588.7078, CriticGrad: 116.8696, Advantage: μ=9.182, σ=32.768, range=[-19.81, 69.57]
Epoch 2, Batch 50/1563, loss: 803.631↓, reward: 7.433↓, critic_reward: 7.267, revenue_rate: 0.2900, distance: 5.1923, memory: 0.0660, power: 0.2167, lr: 0.000400, took: 376.954s
Batch 50: Reward: 8.0951, Loss: 810.4280, Revenue: 0.3370, LoadBalance: 0.7749, Tasks: [S0:1075(33.6%), S1:1014(31.7%), S2:1111(34.7%)], ActorGrad: 1051.0242, CriticGrad: 23.2875, Advantage: μ=0.823, σ=28.681, range=[-19.57, 64.53]
Epoch 2, Batch 100/1563, loss: 797.763↓, reward: 7.794↓, critic_reward: 7.291, revenue_rate: 0.2876, distance: 5.1351, memory: 0.0620, power: 0.2164, lr: 0.000400, took: 369.300s
Batch 100: Reward: 15.8197, Loss: 1079.6238, Revenue: 0.3129, LoadBalance: 0.8151, Tasks: [S0:964(32.0%), S1:1016(33.8%), S2:1028(34.2%)], ActorGrad: 1424.6359, CriticGrad: 111.1106, Advantage: μ=8.440, σ=32.006, range=[-19.39, 61.40]
